package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLPresensiRepository struct {
	mysql.Repository
}

// NewMySQLPresensiRepository func
func NewMySQLPresensiRepository(conn *sql.DB) domain.PresensiRepository {
	return &mySQLPresensiRepository{mysql.Repository{Conn: conn}}
}

func (m *mySQLPresensiRepository) Fetch() ([]domain.Presensi, error) {
	results, err := m.QueryArrayOld("select * from hrm_trans_import_presensi")
	if err != nil {
		log.Printf("getting schedule error: %v\n", err)
		return nil, err
	}

	log.Printf("total data: %d", len(results))

	resultJSON, err := json.<PERSON>(results)
	if err != nil {
		log.Printf("marshalling error: %v\n", err)
	}

	var result []domain.Presensi
	err = json.Unmarshal(resultJSON, &result)
	if err != nil {
		log.Printf("unmarshalling error: %v\n", err)
	}
	return result, nil
}

func (m *mySQLPresensiRepository) ValidateEmployeeNIKs(niks []string) ([]domain.EmployeeNIK, error) {
	if len(niks) == 0 {
		return []domain.EmployeeNIK{}, nil
	}

	query := "SELECT employee_fkid, nik FROM hrm_employee WHERE nik IN @niks"
	query, params := mysql.MapParam(query, map[string]any{
		"niks": niks,
	})

	var employeeNIKs []domain.EmployeeNIK
	err := m.Query(query, params...).Model(&employeeNIKs)
	return employeeNIKs, err
}

func (m *mySQLPresensiRepository) CheckExistingHashes(hashes []string) ([]string, error) {
	if len(hashes) == 0 {
		return []string{}, nil
	}

	// Create placeholders for IN clause
	placeholders := make([]string, len(hashes))
	args := make([]interface{}, len(hashes))
	for i, hash := range hashes {
		placeholders[i] = "?"
		args[i] = hash
	}

	query := fmt.Sprintf("SELECT tip_hash FROM hrm_trans_import_presensi WHERE tip_hash IN (%s)", strings.Join(placeholders, ","))

	results, err := m.QueryArrayOld(query, args...)
	if err != nil {
		log.Printf("checking existing hashes error: %v\n", err)
		return nil, err
	}

	var existingHashes []string
	for _, result := range results {
		if hash, ok := result["tip_hash"].(string); ok {
			existingHashes = append(existingHashes, hash)
		}
	}

	return existingHashes, nil
}

func (m *mySQLPresensiRepository) InsertPresensiData(data []domain.PresensiTransformed, overwrite bool) error {
	if len(data) == 0 {
		return nil
	}

	return m.WithTransaction(func(tx mysql.Transaction) error {
		// If overwrite is true, delete existing records first
		if overwrite {
			var hashes []string
			for _, record := range data {
				hashes = append(hashes, record.TipHash)
			}

			if len(hashes) > 0 {
				// Create delete conditions for each hash
				for _, hash := range hashes {
					deleteConditions := map[string]any{
						"tip_hash": hash,
					}
					result := tx.Delete("hrm_trans_import_presensi", deleteConditions)
					if result != nil {
						if rowsAffected, err := result.RowsAffected(); err == nil {
							log.Printf("deleted %d existing record for hash: %s", rowsAffected, hash)
						}
					}
				}
				log.Printf("deleted existing records for %d hashes", len(hashes))
			}
		}

		// Prepare data for bulk insert
		var bulkInsertData []map[string]any
		for _, record := range data {
			recordData := map[string]any{
				"tip_outlet_id":     record.TipOutletID,
				"tip_nik":           record.TipNik,
				"tip_nama_karyawan": record.TipNamaKaryawan,
				"tip_tanggal":       record.TipTanggal,
				"tip_jam":           record.TipJam,
				"tip_kode":          record.TipKode,
				"tip_hash":          record.TipHash,
				"employee_id":       record.EmployeeID,
			}
			bulkInsertData = append(bulkInsertData, recordData)
		}

		// Execute bulk insert
		result := tx.BulkInsert("hrm_trans_import_presensi", bulkInsertData)
		if result != nil {
			if rowsAffected, err := result.RowsAffected(); err == nil {
				log.Printf("successfully inserted %d presensi records", rowsAffected)
			}
		}

		return nil
	})
}
