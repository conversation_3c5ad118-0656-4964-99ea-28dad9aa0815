package mysql

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/backend/api-hrm/core/log"
	mysql "gitlab.com/backend/api-hrm/core/mysql"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

type mySQLMessagingRepository struct {
	mysql.Repository
}

func NewMySQLMessagingRepository(conn *sql.DB) domain.MessagingRepository {
	return &mySQLMessagingRepository{mysql.Repository{Conn: conn}}
}

func (me *mySQLMessagingRepository) AddMessage(message map[string]interface{}) error {
	_, _, err := me.InsertGetLastID("system_notification", message)
	return err
}

func (me *mySQLMessagingRepository) AddDeviceInfo(info domain.DeviceCreateRequest, userType string, userID string) error {
	data := map[string]interface{}{
		"user_type":    userType,
		"user_id":      userID,
		"device":       info.Device,
		"device_info":  info.DeviceInfo,
		"token":        info.Token,
		"app":          "hrm",
		"data_created": time.Now().UnixMilli(),
		"data_updated": time.Now().UnixMilli(),
	}

	res, _, err := me.InsertGetLastID("user_notification_token", data)
	if err != nil {
		fmt.Printf("insert device token error: %v \n", err)
		fmt.Printf("insert device token error: %v \n", res)
		return err
	}
	return err
}

func (me *mySQLMessagingRepository) FetchUserDevice(empID int) ([]domain.Device, error) {
	query := "SELECT id, user_type, user_id, device, device_info, token FROM user_notification_token WHERE user_id=?"
	res, err := me.QueryArrayOld(query, empID)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	resJSON, err := json.Marshal(res)
	if err != nil {
		log.IfError(err)
		return nil, err
	}
	var results []domain.Device
	err = json.Unmarshal(resJSON, &results)
	return results, err
}

func (me *mySQLMessagingRepository) FetchMessages(empID int, tipe string) ([]domain.Message, error) {
	var args []interface{}
	var results []domain.Message
	args = append(args, cast.ToString(empID), tipe)
	query := "SELECT notification_id, title, message, is_read, is_viewed, data_created, type, receiver_id, admin_fkid,notification_type, notification_data FROM system_notification WHERE receiver_id=? AND type = ? ORDER BY notification_id DESC"
	res := me.Query(query, args...).PrintSql()
	fmt.Println(res.ArrayMap())
	res.Model(&results)
	return results, nil
}

func (me *mySQLMessagingRepository) DeleteNotif(notifID int) error {
	where := map[string]interface{}{
		"notification_id": notifID,
	}

	_, err := me.Deletes("system_notification", where)
	return err
}

func (me *mySQLMessagingRepository) UpdateMessageView(data []map[string]interface{}) error {
	err := me.BulkUpdate("system_notification", "notification_id", data)
	return err
}

func (me *mySQLMessagingRepository) UpdateMessageRead(data []map[string]interface{}) error {
	err := me.BulkUpdate("system_notification", "notification_id", data)
	return err
}

func (me *mySQLMessagingRepository) DeleteMessages(msgID []string) error {
	_, err := me.BulkDeletes("system_notification", msgID, "notification_id")
	return err
}

func (me *mySQLMessagingRepository) FetchUserDeviceByToken(token string) (domain.Device, error) {
	var result domain.Device
	query := "SELECT id, user_type, user_id, device, device_info, token FROM user_notification_token WHERE token LIKE " + "'%" + token + "%'"
	err := me.Query(query).Model(&result)
	if log.IfError(err) {
		return domain.Device{}, err
	}
	return result, err
}

func (me *mySQLMessagingRepository) UpdateUserDevice(data map[string]interface{}, where string) error {
	var dataArr []map[string]interface{}
	data["id"] = where
	var dadataArr = append(dataArr, data)
	res, err := me.SingleUpdate("user_notification_token", "id", dadataArr)
	fmt.Println(res.LastInsertId())
	return err
}
