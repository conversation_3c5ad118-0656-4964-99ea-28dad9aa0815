package domain

// HrmMasterType struct
type HrmMasterType struct {
	TypeID    int    `json:"type_id"`
	AdminFkid int    `json:"admin_fkid"`
	TypeName  string `json:"type_name" validate:"required|string" message:"Nama tipe harus di isi"`
	TypeHours int    `json:"type_hours" validate:"required|int|maxLen:2|max:24" message:"required:Tipe jam harus di isi|maxLen:Maximum 2 character|max:Maximum 24 jam"`
	RestHours int    `json:"rest_hours" validate:"required|int|maxLen:2|max:24" message:"required:Jam istrahat harus di isi|maxLen:Maximum 2 character|max:Maximum 24 jam"`
}

// HrmType struct
type HrmType struct {
	No        int    `json:"NO"`
	TypeID    int    `json:"ACTION"`
	AdminFkid int    `json:"admin_fkid"`
	TypeName  string `json:"TYPE_NAME"`
	TypeHours int    `json:"TYPE_HOURS"`
	RestHours int    `json:"REST_HOURS"`
}

// TypeContract inteface
type TypeContract interface {
	Fetch() ([]HrmMasterType, error)
	FetchAll(id int) ([]HrmType, error)
	FetchByID(id int64) (HrmMasterType, error)
	Add(masterType HrmMasterType, adminID int) error
	Update(masterType HrmMasterType, adminID int) error
	Delete(HrmMasterType) error
}

// TypeUseCase interface
type TypeUseCase interface {
	TypeContract
}

// TypeRepository interface
type TypeRepository interface {
	TypeContract
}
