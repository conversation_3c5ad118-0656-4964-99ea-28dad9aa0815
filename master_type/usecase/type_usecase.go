package usecase

import (
	"gitlab.com/backend/api-hrm/domain"
)

type typeUseCase struct {
	typeRepository domain.TypeRepository
}

// NewTypeUseCase func
func NewTypeUseCase(t domain.TypeRepository) domain.TypeUseCase {
	return &typeUseCase{typeRepository: t}
}

func (t *typeUseCase) FetchAll(id int) ([]domain.HrmType, error) {
	return t.typeRepository.FetchAll(id)
}

func (t *typeUseCase) Fetch() ([]domain.HrmMasterType, error) {
	return t.typeRepository.Fetch()
}

func (t *typeUseCase) FetchByID(id int64) (domain.HrmMasterType, error) {
	return t.typeRepository.FetchByID(id)
}

func (t *typeUseCase) Add(types domain.HrmMasterType, adminID int) error {
	return t.typeRepository.Add(types, adminID)
}

func (t *typeUseCase) Update(types domain.HrmMasterType, adminID int) error {
	return t.typeRepository.Update(types, adminID)
}

func (t *typeUseCase) Delete(types domain.HrmMasterType) error {
	return t.typeRepository.Delete(types)
}
