package http

import (
	"fmt"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/gookit/validate"
	"gitlab.com/backend/api-hrm/core/log"
	"gitlab.com/backend/api-hrm/core/util/cast"
	"gitlab.com/backend/api-hrm/domain"
)

// TypeHandler struct
type TypeHandler struct {
	TypeUseCase domain.TypeUseCase
}

// NewTypeHandler handler
func NewTypeHandler(app *fiber.App, uc domain.TypeUseCase) {
	handler := &TypeHandler{TypeUseCase: uc}
	v1 := app.Group("/v1")
	v1.Get("/types", handler.Fetch)
	v1.Get("/all_type", handler.FetchAll)
	v1.Get("/type/:id", handler.FetchByID)
	v1.Post("/type/add", handler.Add)
	v1.Post("/type/update", handler.Update)
	v1.Post("/type/delete", handler.Delete)
}

// FetchAll data type handler
// @Summary Get all employee types with business ID
// @Description Get all employee types filtered by business ID from the header
// @Tags type
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param business_id header string true "Business ID"
// @Success 200 {array} domain.HrmType "List of employee types"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/all_type [get]
func (t *TypeHandler) FetchAll(c *fiber.Ctx) error {
	// adminID := c.Get("user_id")
	businessId := c.Get("business_id")
	businessID, _ := strconv.Atoi(businessId)

	tipe, err := t.TypeUseCase.FetchAll(businessID)
	if err != nil {
		fmt.Println("error: ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching master type",
			"error":   err,
		})
	}
	return c.JSON(tipe)
}

// Fetch all data type handler
// @Summary Get all employee types
// @Description Get all employee types without filtering
// @Tags type
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Success 200 {array} domain.HrmMasterType "List of all employee types"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/types [get]
func (t *TypeHandler) Fetch(c *fiber.Ctx) error {
	tipe, err := t.TypeUseCase.Fetch()
	if err != nil {
		fmt.Println("error: ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching master type",
			"error":   err,
		})
	}
	return c.JSON(tipe)
}

// FetchByID type handler
// @Summary Get employee type by ID
// @Description Get a specific employee type by its ID
// @Tags type
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param id path int true "Type ID"
// @Success 200 {object} domain.HrmMasterType "Employee type details"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/type/{id} [get]
func (t *TypeHandler) FetchByID(c *fiber.Ctx) error {
	type_id := c.Params("id")
	typeID, _ := strconv.Atoi(type_id)
	tipe, err := t.TypeUseCase.FetchByID(cast.ToInt64(typeID))
	if err != nil {
		fmt.Println("error: ", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error fetching type by ID",
			"error":   err,
		})
	}

	return c.JSON(tipe)
}

// Add type handler
// @Summary Add a new employee type
// @Description Create a new employee type with validation
// @Tags type
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param user_id header string true "User ID"
// @Param type body domain.HrmMasterType true "Employee type information"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Validation error"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/type/add [post]
func (t *TypeHandler) Add(c *fiber.Ctx) error {
	var body domain.HrmMasterType
	c.BodyParser(&body)

	// input validation validation
	data, err := validate.FromStruct(body)
	if err != nil {
		fmt.Printf("error validate form: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "error validating form",
			"error":   err,
		})
	}
	v := data.Create()
	admin_id := c.Get("user_id")
	adminID, _ := strconv.Atoi(admin_id)
	if v.Validate() {
		err := t.TypeUseCase.Add(body, adminID)
		if err != nil {
			return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "Saving data failed",
				"status":  0,
				"error":   err,
			})
		}
		return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
			"message": "Saving data success",
			"status":  1,
			"error":   err,
		})
	}
	// validation return json
	var errField map[string]string
	if v.Errors.HasField("TypeName") {
		errField = map[string]string{"inputTypeName": v.Errors.Field("TypeName")["required"]}
	} else if v.Errors.HasField("TypeHours") {
		if v.Errors.Field("TypeHours")["required"] != "" {
			errField = map[string]string{
				"inputTypeHours": v.Errors.Field("TypeHours")["required"],
			}
		} else if v.Errors.Field("TypeHours")["max"] != "" {
			errField = map[string]string{
				"inputTypeHours": v.Errors.Field("TypeHours")["max"],
			}
		} else if v.Errors.Field("TypeHours")["maxLen"] != "" {
			errField = map[string]string{
				"inputTypeHours": v.Errors.Field("TypeHours")["maxLen"],
			}
		}
	} else if v.Errors.HasField("RestHours") {
		if v.Errors.Field("RestHours")["required"] != "" {
			errField = map[string]string{
				"inputTypeRest": v.Errors.Field("RestHours")["required"],
			}
		} else if v.Errors.Field("RestHours")["max"] != "" {
			errField = map[string]string{
				"inputTypeRest": v.Errors.Field("RestHours")["max"],
			}
		} else if v.Errors.Field("RestHours")["maxLen"] != "" {
			errField = map[string]string{
				"inputTypeRest": v.Errors.Field("RestHours")["maxLen"],
			}
		}
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": errField,
		"status":  0,
		"error":   nil,
	})
}

// Update type handler
// @Summary Update an existing employee type
// @Description Update an employee type with validation
// @Tags type
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param user_id header string true "User ID"
// @Param type body domain.HrmMasterType true "Updated employee type information"
// @Success 200 {object} object "Success response with status"
// @Failure 400 {object} object "Validation error"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/type/update [post]
func (t *TypeHandler) Update(c *fiber.Ctx) error {
	var body domain.HrmMasterType
	c.BodyParser(&body)
	// input validation validation
	data, err := validate.FromStruct(body)
	if err != nil {
		fmt.Printf("validate form error: %v", err)
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "validating form error",
			"error":   err,
		})
	}
	v := data.Create()
	admin_id := c.Get("user_id")
	adminID, _ := strconv.Atoi(admin_id)
	if v.Validate() {
		err := t.TypeUseCase.Update(body, adminID)
		if err != nil {
			c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
				"message": "Updating data failed",
				"status":  0,
				"error":   err,
			})
		}
		return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
			"message": "Updating data success",
			"status":  1,
			"error":   err,
		})
	}
	log.Info("validation failed: %v ", v.Errors)
	// validation return json
	var errField map[string]string
	if v.Errors.HasField("TypeName") {
		errField = map[string]string{"inputTypeName": v.Errors.Field("TypeName")["required"]}
	} else if v.Errors.HasField("TypeHours") {
		if v.Errors.Field("TypeHours")["required"] != "" {
			errField = map[string]string{
				"inputTypeHours": v.Errors.Field("TypeHours")["required"],
			}
		} else if v.Errors.Field("TypeHours")["max"] != "" {
			errField = map[string]string{
				"inputTypeHours": v.Errors.Field("TypeHours")["max"],
			}
		} else if v.Errors.Field("TypeHours")["maxLen"] != "" {
			errField = map[string]string{
				"inputTypeHours": v.Errors.Field("TypeHours")["maxLen"],
			}
		}
	} else if v.Errors.HasField("RestHours") {
		if v.Errors.Field("RestHours")["required"] != "" {
			errField = map[string]string{
				"inputTypeRest": v.Errors.Field("RestHours")["required"],
			}
		} else if v.Errors.Field("RestHours")["max"] != "" {
			errField = map[string]string{
				"inputTypeRest": v.Errors.Field("RestHours")["max"],
			}
		} else if v.Errors.Field("RestHours")["maxLen"] != "" {
			errField = map[string]string{
				"inputTypeRest": v.Errors.Field("RestHours")["maxLen"],
			}
		}
	} else {
		errField = map[string]string{
			"error": v.Errors.String(),
		}
	}

	return c.Status(fiber.StatusBadRequest).JSON(&fiber.Map{
		"message": errField,
		"status":  0,
		"error":   fmt.Sprintf("validation failed: %v", v.Errors),
	})
}

// Delete type handler
// @Summary Delete an employee type
// @Description Delete an existing employee type by ID
// @Tags type
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer token"
// @Param type body domain.HrmMasterType true "Employee type to delete (only type_id is required)"
// @Success 200 {object} object "Success response with status"
// @Failure 500 {object} object "Internal server error"
// @Router /v1/type/delete [post]
func (t *TypeHandler) Delete(c *fiber.Ctx) error {
	var body domain.HrmMasterType
	c.BodyParser(&body)
	err := t.TypeUseCase.Delete(body)
	if err != nil {
		return c.Status(fiber.ErrInternalServerError.Code).JSON(&fiber.Map{
			"message": "Delete data failed",
			"status":  0,
			"error":   err,
		})
	}
	return c.Status(c.Response().StatusCode()).JSON(&fiber.Map{
		"message": "Delete success",
		"status":  1,
		"error":   nil,
	})
}
