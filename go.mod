module gitlab.com/backend/api-hrm

go 1.23.0

toolchain go1.24.2

require (
	cloud.google.com/go/storage v1.12.0
	firebase.google.com/go v3.13.0+incompatible
	github.com/DATA-DOG/go-sqlmock v1.5.0
	github.com/dave/jennifer v1.4.1
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/go-sql-driver/mysql v1.5.0
	github.com/gofiber/fiber/v2 v2.52.6
	github.com/gofiber/swagger v1.1.1
	github.com/golang/protobuf v1.5.0
	github.com/gookit/validate v1.5.4
	github.com/joho/godotenv v1.3.0
	github.com/mitchellh/mapstructure v1.4.0
	github.com/swaggo/swag v1.16.4
	github.com/xuri/excelize/v2 v2.9.1
	golang.org/x/crypto v0.38.0
	google.golang.org/api v0.40.0
	google.golang.org/grpc v1.35.0
	google.golang.org/protobuf v1.36.6
	gorm.io/driver/mysql v1.0.3
	gorm.io/gorm v1.20.9
)

require (
	cloud.google.com/go v0.75.0 // indirect
	cloud.google.com/go/firestore v1.5.0 // indirect
	github.com/KyleBanks/depth v1.2.1 // indirect
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/go-openapi/jsonpointer v0.21.1 // indirect
	github.com/go-openapi/jsonreference v0.21.0 // indirect
	github.com/go-openapi/spec v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.1 // indirect
	github.com/golang/groupcache v0.0.0-**************-8c9f03a8e57e // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/googleapis/gax-go/v2 v2.0.5 // indirect
	github.com/gookit/filter v1.2.2 // indirect
	github.com/gookit/goutil v0.6.18 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.1 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/jstemmer/go-junit-report v0.9.1 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/mailru/easyjson v0.9.0 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.4 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/swaggo/files/v2 v2.0.2 // indirect
	github.com/tiendc/go-deepcopy v1.6.0 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.62.0 // indirect
	github.com/xuri/efp v0.0.1 // indirect
	github.com/xuri/nfp v0.0.1 // indirect
	go.opencensus.io v0.22.5 // indirect
	golang.org/x/lint v0.0.0-20201208152925-83fdc39ff7b5 // indirect
	golang.org/x/mod v0.24.0 // indirect
	golang.org/x/net v0.40.0 // indirect
	golang.org/x/oauth2 v0.0.0-20210218202405-ba52d332ba99 // indirect
	golang.org/x/sync v0.14.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/text v0.25.0 // indirect
	golang.org/x/tools v0.33.0 // indirect
	google.golang.org/appengine v1.6.7 // indirect
	google.golang.org/genproto v0.0.0-20210222152913-aa3ee6e6a81c // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
