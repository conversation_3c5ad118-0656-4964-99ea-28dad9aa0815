package usecase

import (
	"os"

	"gitlab.com/backend/api-hrm/core/util/bucket"
)

func (e *employeeUseCase) uploadAttachment(attachment string, employeeFkid string) (string, error) {
	file, err := os.Open(attachment)
	if err != nil {
		return "", err
	}
	defer file.Close()
	//if employeeId is empty, e.g when creating new user (has no yet id)
	if employeeFkid == "" {
		employeeFkid = "0" //use 0 as default
	}
	url, err := bucket.UploadBucket(file, attachment, employeeFkid, true)
	if err != nil {
		return "", err
	}
	return url, nil
}
