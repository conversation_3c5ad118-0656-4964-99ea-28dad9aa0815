package mysql

import (
	"database/sql"
	"errors"
	"fmt"
)

type Transaction interface {
	Insert(table string, data map[string]any) sql.Result
	Update(table string, data map[string]any, whereCond string, whereParams ...any) sql.Result
	Delete(table string, where map[string]any) sql.Result
	BulkInsert(table string, data []map[string]any) sql.Result
}

type TxFn func(tx Transaction) error

type SqlTx struct {
	Tx *sql.Tx
}

func (s *SqlTx) Insert(table string, data map[string]any) sql.Result {
	query, values := BuildInsertQuery(table, data)
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		fmt.Printf("%v \nquery : %s", err, getSQLRaw(query, values...))
		panic(err)
	}
	return resp
}

func (s *SqlTx) Update(table string, data map[string]any, whereCond string, whereParams ...any) sql.Result {
	values := make([]any, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereCond

	for _, param := range whereParams {
		values = append(values, param)
	}

	res, err := s.Tx.Exec(query, values...)
	if err != nil {
		panic(fmt.Sprintf("%v \nquery : %s", err, getSQLRaw(query, values...)))
	}

	return res
}

func (s *SqlTx) Delete(table string, where map[string]any) sql.Result {
	query, wheres := BuildDeleteQuery(table, where)

	res, err := s.Tx.Exec(query, wheres...)
	if err != nil {
		panic(fmt.Sprintf("%v \nquery: %v, params: %v,query full : %s", err, query, wheres, getSQLRaw(query, wheres...)))
	}

	return res
}

func (s *SqlTx) BulkInsert(table string, data []map[string]any) sql.Result {
	if len(data) == 0 {
		fmt.Println("can not BulkInsert, no data given")
		return nil
	}
	query, values := BuildBulkInsertQuery(table, data)
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		fmt.Printf("%v \nquery : %s", err, getSQLRaw(query, values...))
		panic(err)
	}
	return resp
}

func (db *Repository) WithTransaction(fn TxFn) (err error) {
	tx, err := db.Conn.Begin()
	if err != nil {
		return err
	}
	sqlTx := &SqlTx{tx}

	defer func() {
		if p := recover(); p != nil {
			sqlTx.Tx.Rollback()
			err = errors.New(fmt.Sprintf("%v", p))
			fmt.Println("rocover(), rolling back transaction, error:", err)
		} else if err != nil {
			// something went wrong, rollback
			fmt.Println("rolling back transaction, error:", err)
			sqlTx.Tx.Rollback()
		} else {
			// all good, commit
			sqlTx.Tx.Commit()
		}
	}()
	err = fn(sqlTx)
	return err
}
