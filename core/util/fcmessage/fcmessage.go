package fcmessage

import (
	"context"
	"fmt"
	"os"

	firebase "firebase.google.com/go"
	"firebase.google.com/go/messaging"
	"google.golang.org/api/option"
)

func SendMessage(empID, title, body string, tokens ...string) error {
	if tokens == nil || len(tokens) == 0 {
		fmt.Println("can not send message with empty token")
		return nil
	}
	opt := option.WithCredentialsFile(os.Getenv("FCM_PATH"))
	fcm, err := firebase.NewApp(context.Background(), nil, opt)
	if err != nil {
		fmt.Printf("(util fcm) error initializing app: %v\n", err)
		return err
	}
	ctx := context.Background()
	client, err := fcm.Messaging(ctx)
	if err != nil {
		fmt.Printf("(util fcm) error initializing client: %v\n", err)
		return err
	}
	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: title,
			Body:  body,
		},
		Tokens: tokens,
	}
	br, err := client.SendMulticast(ctx, message)
	if err != nil {
		fmt.Printf("(util fcm) send message error: %v\n", err)
		return err
	}

	fmt.Printf("success count: %v", br.SuccessCount)
	for x, res := range br.Responses {
		if res.Success {
			fmt.Println("success: ", x)
		}
	}

	if br.FailureCount > 0 {
		var failedTokens []string
		for idx, resp := range br.Responses {
			if !resp.Success {
				failedTokens = append(failedTokens, tokens[idx])
			}
		}
		fmt.Println("List of tokens that caused failures: ", failedTokens)
	}
	// return "ok"
	return nil
}
