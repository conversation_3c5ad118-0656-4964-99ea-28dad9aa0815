package main

import (
	"database/sql"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"github.com/xuri/excelize/v2"
)

// Response structures
type UploadResponse struct {
	Status   int    `json:"status"`
	Message  string `json:"message"`
	OutletID string `json:"outletId,omitempty"`
}

// Employee and Shift structures
type Employee struct {
	NIK      int    `json:"nik"`
	Name     string `json:"name"`
	TypeFKID string `json:"type_fkid"`
}

type Shift struct {
	ShiftType string `json:"shift_type"`
	ShiftIn   string `json:"shift_in"`
	ShiftOut  string `json:"shift_out"`
}

// Attendance record structure
type AttendanceRecord struct {
	NIK         int    `json:"nik"`
	Name        string `json:"name"`
	Date        string `json:"date"`
	Time        string `json:"time"`
	Description string `json:"description"`
}

// Database models
type Employee struct {
	NIK        int    `json:"nik" db:"nik"`
	Name       string `json:"name" db:"name"`
	TypeFKID   string `json:"type_fkid" db:"type_fkid"`
	EmployeeID int    `json:"employee_id" db:"employee_fkid"`
}

type Shift struct {
	ShiftID   int    `json:"shift_id" db:"shift_id"`
	ShiftType string `json:"shift_type" db:"shift_type"`
	ShiftCode string `json:"shift_code" db:"shift_code"`
	ShiftIn   string `json:"shift_in" db:"shift_in"`
	ShiftOut  string `json:"shift_out" db:"shift_out"`
}

type ImportPresensi struct {
	TipOutletID     int    `db:"tip_outlet_id"`
	TipNIK          string `db:"tip_nik"`
	TipNamaKaryawan string `db:"tip_nama_karyawan"`
	TipTanggal      string `db:"tip_tanggal"`
	TipJam          string `db:"tip_jam"`
	TipKode         string `db:"tip_kode"`
	TipHash         string `db:"tip_hash"`
	EmployeeID      int    `db:"employee_id"`
}

// Service interface for database operations
type PresensiService interface {
	GetEmployees(outletID string) ([]Employee, error)
	GetShifts(outletID string) ([]Shift, error)
	InsertDataPresensi(data [][]interface{}, outletID string, overwrite bool) (map[string]interface{}, error)
}

// Handler struct
type AttendanceHandler struct {
	presensiService PresensiService
}

func NewAttendanceHandler(service PresensiService) *AttendanceHandler {
	return &AttendanceHandler{
		presensiService: service,
	}
}

// Main handler function that combines upload and processing
func (h *AttendanceHandler) ImportAttendanceData(c *gin.Context) {
	// Parse multipart form
	err := c.Request.ParseMultipartForm(32 << 20) // 32MB max
	if err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Status:  0,
			Message: "Failed to parse form data",
		})
		return
	}

	// Get file from form
	file, fileHeader, err := c.Request.FormFile("inputFileImport")
	if err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Status:  0,
			Message: "No file uploaded",
		})
		return
	}
	defer file.Close()

	// Validate file extension
	if !isValidExcelFile(fileHeader.Filename) {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Status:  0,
			Message: "Only .xls and .xlsx files are allowed",
		})
		return
	}

	// Get form parameters
	outletID := c.PostForm("outletId")
	overwriteStr := c.PostForm("overwrite")
	overwrite, _ := strconv.ParseBool(overwriteStr)

	// Create temporary file
	tempFile, err := os.CreateTemp("", "import_*.xlsx")
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Status:  0,
			Message: "Failed to create temporary file",
		})
		return
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// Copy uploaded file to temporary file
	_, err = io.Copy(tempFile, file)
	if err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Status:  0,
			Message: "Failed to save uploaded file",
		})
		return
	}

	// Process the Excel file
	response := h.processExcelFile(tempFile.Name(), outletID, overwrite)
	c.JSON(http.StatusOK, response)
}

// Process Excel file and import data
func (h *AttendanceHandler) processExcelFile(filePath, outletID string, overwrite bool) UploadResponse {
	// Open Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return UploadResponse{
			Status:  0,
			Message: "File excel is not readable. Please try again",
		}
	}
	defer f.Close()

	// Get first sheet
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return UploadResponse{
			Status:  0,
			Message: "No sheet found in Excel file",
		}
	}

	// Read all rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return UploadResponse{
			Status:  0,
			Message: "Failed to read Excel rows",
		}
	}

	if len(rows) < 6 {
		return UploadResponse{
			Status:  0,
			Message: "Excel file must have at least 6 rows",
		}
	}

	// Extract outlet ID from header (first 5 rows are headers)
	extractedOutletID := ""
	if len(rows) > 0 && len(rows[0]) > 1 {
		re := regexp.MustCompile(`\d+`)
		matches := re.FindAllString(rows[0][1], -1)
		if len(matches) > 0 {
			extractedOutletID = strings.Join(matches, "")
		}
	}

	// If outletID is not provided, use extracted one
	if outletID == "" {
		outletID = extractedOutletID
	}

	// Convert rows to data (skip first 5 header rows)
	var rowData [][]interface{}
	for i := 5; i < len(rows); i++ {
		row := make([]interface{}, len(rows[i]))
		for j, cell := range rows[i] {
			row[j] = cell
		}
		rowData = append(rowData, row)
	}

	// Get employees and shifts from database
	employees, err := h.presensiService.GetEmployees(outletID)
	if err != nil {
		return UploadResponse{
			Status:  0,
			Message: "Failed to get employee data",
		}
	}

	shifts, err := h.presensiService.GetShifts(outletID)
	if err != nil {
		return UploadResponse{
			Status:  0,
			Message: "Failed to get shift data",
		}
	}

	// Create lookup maps
	nikMap := make(map[int]Employee)
	nameMap := make(map[string]Employee)
	for _, emp := range employees {
		nikMap[emp.NIK] = emp
		nameMap[emp.Name] = emp
	}

	shiftMap := make(map[string]Shift)
	for _, shift := range shifts {
		shiftMap[shift.ShiftType] = shift
	}

	// Process attendance data
	processedData, validationErrors := h.processAttendanceData(rowData, nikMap, nameMap, shiftMap)

	// Check for validation errors
	if len(validationErrors) > 0 {
		return UploadResponse{
			Status:  0,
			Message: strings.Join(validationErrors, "<br>"),
		}
	}

	// Insert data into database
	result, err := h.presensiService.InsertDataPresensi(processedData, outletID, overwrite)
	if err != nil {
		return UploadResponse{
			Status:  0,
			Message: "Failed to insert data: " + err.Error(),
		}
	}

	if result["status"].(bool) {
		return UploadResponse{
			Status:   1,
			Message:  fmt.Sprintf("%v data imported successfully", result["data"]),
			OutletID: outletID,
		}
	}

	return UploadResponse{
		Status:  0,
		Message: "No data imported",
	}
}

// Process attendance data and assign descriptions
func (h *AttendanceHandler) processAttendanceData(data [][]interface{}, nikMap map[int]Employee, nameMap map[string]Employee, shiftMap map[string]Shift) ([][]interface{}, []string) {
	var validationErrors []string
	var invalidNIKs, invalidNames []string

	// Validate NIKs and Names
	for i, row := range data {
		if len(row) < 4 {
			continue
		}

		// Check NIK
		nikStr := fmt.Sprintf("%v", row[0])
		if nikStr != "" {
			nik, err := strconv.Atoi(nikStr)
			if err == nil {
				if _, exists := nikMap[nik]; !exists {
					invalidNIKs = append(invalidNIKs, strconv.Itoa(i+6))
				}
			}
		}

		// Check Name
		nameStr := fmt.Sprintf("%v", row[1])
		if nameStr != "" {
			if _, exists := nameMap[nameStr]; !exists {
				invalidNames = append(invalidNames, strconv.Itoa(i+6))
			}
		}
	}

	if len(invalidNIKs) > 0 {
		validationErrors = append(validationErrors, "Invalid NIK in rows: "+strings.Join(invalidNIKs, ","))
	}
	if len(invalidNames) > 0 {
		validationErrors = append(validationErrors, "Invalid Name in rows: "+strings.Join(invalidNames, ","))
	}

	if len(validationErrors) > 0 {
		return nil, validationErrors
	}

	// Assign descriptions based on shift times
	h.assignShiftDescriptions(data, nikMap, shiftMap)
	h.assignBreakDescriptions(data, nikMap, shiftMap)
	h.assignSplitDescriptions(data, nikMap, shiftMap)

	// Group by employee and date, then fill missing descriptions
	processedData := h.fillMissingDescriptions(data)

	return processedData, nil
}

// Assign shift in/out descriptions
func (h *AttendanceHandler) assignShiftDescriptions(data [][]interface{}, nikMap map[int]Employee, shiftMap map[string]Shift) {
	for _, row := range data {
		if len(row) < 5 {
			row = append(row, "")
		}

		nikStr := fmt.Sprintf("%v", row[0])
		timeStr := fmt.Sprintf("%v", row[3])

		if nikStr == "" || timeStr == "" {
			continue
		}

		nik, err := strconv.Atoi(nikStr)
		if err != nil {
			continue
		}

		employee, exists := nikMap[nik]
		if !exists {
			continue
		}

		shift, exists := shiftMap[employee.TypeFKID]
		if !exists {
			continue
		}

		// Parse time
		recordTime, err := time.Parse("15:04:05", timeStr)
		if err != nil {
			continue
		}

		// Check shift in (within 30 minutes)
		shiftInTime, _ := time.Parse("15:04:05", shift.ShiftIn)
		if isWithinTimeRange(recordTime, shiftInTime, 30) {
			row[4] = "1" // Shift in
			continue
		}

		// Check shift out (within 120 minutes)
		shiftOutTime, _ := time.Parse("15:04:05", shift.ShiftOut)
		if isWithinTimeRange(recordTime, shiftOutTime, 120) {
			row[4] = "4" // Shift out
		}
	}
}

// Assign break in/out descriptions
func (h *AttendanceHandler) assignBreakDescriptions(data [][]interface{}, nikMap map[int]Employee, shiftMap map[string]Shift) {
	for i, row := range data {
		if len(row) < 5 || fmt.Sprintf("%v", row[4]) != "" {
			continue
		}

		// Check if next record exists and is from same employee
		if i+1 < len(data) {
			nextRow := data[i+1]
			if len(nextRow) >= 5 && fmt.Sprintf("%v", row[0]) == fmt.Sprintf("%v", nextRow[0]) {
				currTime, _ := time.Parse("15:04:05", fmt.Sprintf("%v", row[3]))
				nextTime, _ := time.Parse("15:04:05", fmt.Sprintf("%v", nextRow[3]))

				if currTime.Before(nextTime) && fmt.Sprintf("%v", nextRow[4]) == "" {
					row[4] = "2" // Break out
				}
			}
		}

		// Check if previous record was break out
		if i > 0 {
			prevRow := data[i-1]
			if len(prevRow) >= 5 && fmt.Sprintf("%v", row[0]) == fmt.Sprintf("%v", prevRow[0]) {
				currTime, _ := time.Parse("15:04:05", fmt.Sprintf("%v", row[3]))
				prevTime, _ := time.Parse("15:04:05", fmt.Sprintf("%v", prevRow[3]))

				if currTime.After(prevTime) && fmt.Sprintf("%v", prevRow[4]) == "2" {
					row[4] = "3" // Break in
				}
			}
		}
	}
}

// Assign split shift descriptions
func (h *AttendanceHandler) assignSplitDescriptions(data [][]interface{}, nikMap map[int]Employee, shiftMap map[string]Shift) {
	for i, row := range data {
		if len(row) < 5 {
			continue
		}

		desc := fmt.Sprintf("%v", row[4])
		if desc != "2" && desc != "3" {
			continue
		}

		// Check for split shift patterns
		if i+1 < len(data) {
			nextRow := data[i+1]
			if len(nextRow) >= 5 && fmt.Sprintf("%v", row[0]) == fmt.Sprintf("%v", nextRow[0]) {
				if i > 0 {
					prevRow := data[i-1]
					if fmt.Sprintf("%v", prevRow[4]) == "4" {
						row[4] = "6" // Split out
					}
				}
			}
		}

		if i > 0 {
			prevRow := data[i-1]
			if len(prevRow) >= 5 && fmt.Sprintf("%v", row[0]) == fmt.Sprintf("%v", prevRow[0]) {
				if fmt.Sprintf("%v", prevRow[4]) == "6" {
					row[4] = "5" // Split in
				}
			}
		}
	}
}

// Fill missing descriptions for complete attendance records
func (h *AttendanceHandler) fillMissingDescriptions(data [][]interface{}) [][]interface{} {
	// Group by employee and date
	groupedData := make(map[string]map[string][][]interface{})

	for _, row := range data {
		if len(row) < 3 {
			continue
		}

		nik := fmt.Sprintf("%v", row[0])
		date := fmt.Sprintf("%v", row[2])

		if _, exists := groupedData[nik]; !exists {
			groupedData[nik] = make(map[string][][]interface{})
		}

		groupedData[nik][date] = append(groupedData[nik][date], row)
	}

	// Fill missing descriptions
	var result [][]interface{}
	requiredDescs := []string{"1", "2", "3", "4", "5", "6"}

	for nik, dateGroup := range groupedData {
		for date, records := range dateGroup {
			// Check which descriptions are missing
			existingDescs := make(map[string]bool)
			for _, record := range records {
				if len(record) >= 5 {
					desc := fmt.Sprintf("%v", record[4])
					if desc != "" {
						existingDescs[desc] = true
					}
				}
			}

			// Add missing descriptions
			for _, reqDesc := range requiredDescs {
				if !existingDescs[reqDesc] && len(records) > 0 {
					firstRecord := records[0]
					if len(firstRecord) >= 3 {
						newRecord := []interface{}{
							firstRecord[0], // NIK
							firstRecord[1], // Name
							firstRecord[2], // Date
							"00:00:00",     // Time
							reqDesc,        // Description
						}
						records = append(records, newRecord)
					}
				}
			}

			result = append(result, records...)
		}
	}

	return result
}

// Helper functions
func isValidExcelFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".xls" || ext == ".xlsx"
}

func isWithinTimeRange(recordTime, shiftTime time.Time, toleranceMinutes int) bool {
	minTime := shiftTime.Add(-time.Duration(toleranceMinutes) * time.Minute)
	maxTime := shiftTime.Add(time.Duration(toleranceMinutes) * time.Minute)
	return recordTime.After(minTime) && recordTime.Before(maxTime)
}

// Router setup
func SetupRoutes(handler *AttendanceHandler) *gin.Engine {
	r := gin.Default()

	// Set max multipart form memory (32MB)
	r.MaxMultipartMemory = 32 << 20

	r.POST("/import-attendance", handler.ImportAttendanceData)

	return r
}

// Database service implementation
type PresensiServiceImpl struct {
	db *sql.DB
}

func NewPresensiService(db *sql.DB) PresensiService {
	return &PresensiServiceImpl{db: db}
}

func (s *PresensiServiceImpl) GetEmployees(outletID string) ([]Employee, error) {
	query := `
		SELECT 
			e.nik,
			em.name,
			e.type_fkid,
			em.employee_id as employee_fkid
		FROM employee_outlet eo
		JOIN employee em ON em.employee_id = eo.employee_fkid
		RIGHT JOIN outlets o ON o.outlet_id = eo.outlet_fkid
		RIGHT JOIN hrm_employee e ON em.employee_id = e.employee_fkid
		WHERE eo.outlet_fkid = ?
		ORDER BY o.outlet_id, em.name ASC
	`

	rows, err := s.db.Query(query, outletID)
	if err != nil {
		return nil, fmt.Errorf("failed to query employees: %w", err)
	}
	defer rows.Close()

	var employees []Employee
	for rows.Next() {
		var emp Employee
		err := rows.Scan(&emp.NIK, &emp.Name, &emp.TypeFKID, &emp.EmployeeID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan employee: %w", err)
		}
		employees = append(employees, emp)
	}

	return employees, nil
}

func (s *PresensiServiceImpl) GetShifts(outletID string) ([]Shift, error) {
	query := `
		SELECT 
			shift_id,
			shift_type,
			shift_code,
			shift_in,
			shift_out
		FROM hrm_master_shift
		WHERE shift_office = ?
	`

	rows, err := s.db.Query(query, outletID)
	if err != nil {
		return nil, fmt.Errorf("failed to query shifts: %w", err)
	}
	defer rows.Close()

	var shifts []Shift
	for rows.Next() {
		var shift Shift
		err := rows.Scan(&shift.ShiftID, &shift.ShiftType, &shift.ShiftCode, &shift.ShiftIn, &shift.ShiftOut)
		if err != nil {
			return nil, fmt.Errorf("failed to scan shift: %w", err)
		}
		shifts = append(shifts, shift)
	}

	return shifts, nil
}

func (s *PresensiServiceImpl) InsertDataPresensi(params [][]interface{}, outletID string, overwrite bool) (map[string]interface{}, error) {
	// Get existing hash codes
	existingHashes, err := s.getExistingHashes(outletID)
	if err != nil {
		return map[string]interface{}{"status": false, "data": 0}, err
	}

	// Get employee mapping
	employeeMap, err := s.getEmployeeMapping(params)
	if err != nil {
		return map[string]interface{}{"status": false, "data": 0}, err
	}

	var dataToInsert []ImportPresensi
	outletIDInt, _ := strconv.Atoi(outletID)

	for _, param := range params {
		if len(param) < 5 {
			continue
		}

		nik := fmt.Sprintf("%v", param[0])
		if nik == "" {
			continue
		}

		employeeID := 0
		if emp, exists := employeeMap[nik]; exists {
			employeeID = emp.EmployeeID
		}

		// Format date
		dateFormatted, err := s.formatDate(fmt.Sprintf("%v", param[2]))
		if err != nil {
			continue
		}

		// Create hash
		clearDate := regexp.MustCompile(`[^0-9]`).ReplaceAllString(dateFormatted, "")
		clearTime := regexp.MustCompile(`[^0-9]`).ReplaceAllString(fmt.Sprintf("%v", param[3]), "")
		hash := nik + outletID + clearDate + clearTime + fmt.Sprintf("%v", param[4])

		// Check if hash exists
		if contains(existingHashes, hash) {
			if !overwrite {
				continue // Skip if not overwriting
			}
			// Delete existing record
			err = s.deleteExistingRecord(nik, outletID, dateFormatted)
			if err != nil {
				return map[string]interface{}{"status": false, "data": 0}, err
			}
		}

		// Prepare data for insertion
		record := ImportPresensi{
			TipOutletID:     outletIDInt,
			TipNIK:          nik,
			TipNamaKaryawan: fmt.Sprintf("%v", param[1]),
			TipTanggal:      dateFormatted,
			TipJam:          fmt.Sprintf("%v", param[3]),
			TipKode:         fmt.Sprintf("%v", param[4]),
			TipHash:         hash,
			EmployeeID:      employeeID,
		}

		dataToInsert = append(dataToInsert, record)
	}

	if len(dataToInsert) == 0 {
		return map[string]interface{}{"status": false, "data": 0}, nil
	}

	// Insert batch
	err = s.insertBatch(dataToInsert)
	if err != nil {
		return map[string]interface{}{"status": false, "data": len(dataToInsert)}, err
	}

	return map[string]interface{}{"status": true, "data": len(dataToInsert)}, nil
}

func (s *PresensiServiceImpl) getExistingHashes(outletID string) ([]string, error) {
	query := "SELECT tip_hash FROM hrm_trans_import_presensi WHERE tip_outlet_id = ?"
	rows, err := s.db.Query(query, outletID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var hashes []string
	for rows.Next() {
		var hash string
		err := rows.Scan(&hash)
		if err != nil {
			return nil, err
		}
		hashes = append(hashes, hash)
	}

	return hashes, nil
}

func (s *PresensiServiceImpl) getEmployeeMapping(params [][]interface{}) (map[string]Employee, error) {
	// Extract unique NIKs
	nikSet := make(map[string]bool)
	for _, param := range params {
		if len(param) > 0 {
			nik := fmt.Sprintf("%v", param[0])
			if nik != "" {
				nikSet[nik] = true
			}
		}
	}

	if len(nikSet) == 0 {
		return make(map[string]Employee), nil
	}

	// Build IN clause
	niks := make([]string, 0, len(nikSet))
	for nik := range nikSet {
		niks = append(niks, nik)
	}

	placeholders := strings.Repeat("?,", len(niks))
	placeholders = placeholders[:len(placeholders)-1] // Remove last comma

	query := fmt.Sprintf("SELECT employee_fkid, nik FROM hrm_employee WHERE nik IN (%s)", placeholders)

	args := make([]interface{}, len(niks))
	for i, nik := range niks {
		args[i] = nik
	}

	rows, err := s.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	employeeMap := make(map[string]Employee)
	for rows.Next() {
		var emp Employee
		err := rows.Scan(&emp.EmployeeID, &emp.NIK)
		if err != nil {
			return nil, err
		}
		employeeMap[fmt.Sprintf("%d", emp.NIK)] = emp
	}

	return employeeMap, nil
}

func (s *PresensiServiceImpl) formatDate(dateStr string) (string, error) {
	// Handle different date formats as in the original PHP code
	if len(dateStr) >= 5 && dateStr[4:5] == "/" {
		// Format: YYYY/MM/DD
		parts := strings.Split(dateStr, "/")
		return strings.Join(parts, "-"), nil
	} else if len(dateStr) >= 5 && dateStr[4:5] == "-" {
		// Format: YYYY-MM-DD
		return dateStr, nil
	} else if len(dateStr) >= 3 && dateStr[2:3] == "/" {
		// Format: DD/MM/YYYY
		parts := strings.Split(dateStr, "/")
		if len(parts) == 3 {
			return fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0]), nil
		}
	} else if len(dateStr) >= 3 && dateStr[2:3] == "-" {
		// Format: DD-MM-YYYY
		parts := strings.Split(dateStr, "-")
		if len(parts) == 3 {
			// Try to parse as DD-MM-YYYY first
			if _, err := time.Parse("02-01-2006", dateStr); err == nil {
				return fmt.Sprintf("%s-%s-%s", parts[2], parts[1], parts[0]), nil
			}
			// Try MM-DD-YYYY
			return fmt.Sprintf("%s-%s-%s", parts[2], parts[0], parts[1]), nil
		}
	}

	return dateStr, nil
}

func (s *PresensiServiceImpl) deleteExistingRecord(nik, outletID, date string) error {
	query := `
		DELETE FROM hrm_trans_import_presensi 
		WHERE tip_nik = ? AND tip_outlet_id = ? AND tip_tanggal = ?
	`
	_, err := s.db.Exec(query, nik, outletID, date)
	return err
}

func (s *PresensiServiceImpl) insertBatch(records []ImportPresensi) error {
	if len(records) == 0 {
		return nil
	}

	// Build batch insert query
	valueStrings := make([]string, 0, len(records))
	valueArgs := make([]interface{}, 0, len(records)*8)

	for _, record := range records {
		valueStrings = append(valueStrings, "(?, ?, ?, ?, ?, ?, ?, ?)")
		valueArgs = append(valueArgs,
			record.TipOutletID,
			record.TipNIK,
			record.TipNamaKaryawan,
			record.TipTanggal,
			record.TipJam,
			record.TipKode,
			record.TipHash,
			record.EmployeeID,
		)
	}

	query := fmt.Sprintf(`
		INSERT INTO hrm_trans_import_presensi 
		(tip_outlet_id, tip_nik, tip_nama_karyawan, tip_tanggal, tip_jam, tip_kode, tip_hash, employee_id) 
		VALUES %s
	`, strings.Join(valueStrings, ","))

	_, err := s.db.Exec(query, valueArgs...)
	return err
}

// Helper function
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// Example usage
func main() {
	// Initialize database connection
	db, err := sql.Open("mysql", "user:password@tcp(localhost:3306)/database")
	if err != nil {
		panic(err)
	}
	defer db.Close()

	// Initialize service and handler
	service := NewPresensiService(db)
	handler := NewAttendanceHandler(service)
	r := SetupRoutes(handler)
	r.Run(":8080")
}
