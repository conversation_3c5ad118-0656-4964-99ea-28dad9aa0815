package main

import (
	"fmt"
	"log"
)

func main() {
	// Create a new file and sheet
	data := make(map[string]any)
	data["name"] = "<PERSON>"
	if data["name"] == "<PERSON>" {
		log.Printf("data is <PERSON>")
	} else {
		log.Printf("data is not <PERSON>")
	}
}

func callme(data []map[string]any) {
	log.Printf("data is empty or nil: %v", data == nil)
	log.Printf("data is empty or nil: %v", len(data) == 0)
	log.Printf("data is empty or nil: %v", data == nil || len(data) == 0)
	log.Printf("data is empty or nil: %v", data == nil || len(data) == 0)
	//loop data and log each item
	for _, item := range data {
		log.Printf("item: %v", item)
	}
	fmt.Println("data: ", data)
}
